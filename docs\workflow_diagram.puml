@startuml
!theme plain
skinparam backgroundColor #FFFFFF
skinparam defaultFontSize 10
skinparam packageStyle rectangle

title MARIS Ship Simulation Complete Workflow
note top : uv run maris --ship ship_kvlcc2.json --scenario scenario_turning_circle.json --out-dir runs/final_test --plot2d

actor User
participant "CLI\n(maris_cli.py)" as CLI
participant "Ship Loader\n(io/ship.py)" as ShipLoader
participant "Scenario Loader\n(io/scenario.py)" as ScenarioLoader
participant "JSON Schema\nValidator" as Validator
participant "Environment\nProvider" as EnvProvider
participant "PID Autopilot\n(control/)" as Autopilot
participant "Control Provider\n(rate integrating)" as ControlProvider
participant "MMG3DOF Model\n(models/mmg3dof/)" as Model
participant "Simulation Runner\n(sim/runner.py)" as Runner
participant "SciPy Integration\n(solve_ivp)" as SciPy
participant "Force Modules\n(forces/)" as Forces
participant "CSV Writer" as CSVWriter
participant "JSONL Writer" as JSONLWriter
participant "Summary Writer" as SummaryWriter
participant "Plot2D\n(viz/plot2d.py)" as Plotter
database "Output Files" as Files

== 1. Initialization Phase ==
User -> CLI: Execute command with arguments
activate CLI

CLI -> CLI: Parse arguments\n--ship, --scenario, --out-dir, --plot2d
CLI -> CLI: Create output directory\nruns/final_test/

== 2. Input Loading & Validation ==
CLI -> ShipLoader: load(ship_kvlcc2.json)
activate ShipLoader
ShipLoader -> Validator: Validate against ship.schema.json
Validator --> ShipLoader: ✓ Valid
ShipLoader -> ShipLoader: Parse geometry, mass_properties,\nmmg coefficients, limits
ShipLoader --> CLI: VesselParams object
deactivate ShipLoader

CLI -> ScenarioLoader: load(scenario_turning_circle.json)
activate ScenarioLoader
ScenarioLoader -> Validator: Validate against scenario.schema.json
Validator --> ScenarioLoader: ✓ Valid
ScenarioLoader -> ScenarioLoader: Parse simulation config,\ninitial state, environment
ScenarioLoader --> CLI: (SimulationConfig, VesselState, env_cfg)
deactivate ScenarioLoader

CLI -> CLI: validate_params(vessel_params)\nvalidate_config(cfg)\nvalidate_initial_state(state0)

== 3. Component Setup ==
CLI -> EnvProvider: create_environment_provider_from_config(env_cfg)
activate EnvProvider
EnvProvider -> EnvProvider: Setup wind: 5 m/s from behind\nSetup current: 0.5 m/s eastward
EnvProvider --> CLI: Environment provider
deactivate EnvProvider

CLI -> Autopilot: RatePIDAutopilot(\nheading PID, speed PID)
activate Autopilot
Autopilot --> CLI: PID controllers ready
deactivate Autopilot

CLI -> ControlProvider: RateIntegratingControlProvider(\nvesselparams, autopilot, waypoint)
activate ControlProvider
ControlProvider -> ControlProvider: Initialize with:\nrpm=0, rudder=0\ntarget: heading=0.1 rad, speed=0.5 m/s
ControlProvider --> CLI: Control provider ready
deactivate ControlProvider

== 4. Output Writers Setup ==
CLI -> CSVWriter: CsvTickWriter(results.csv, headers)
activate CSVWriter
CSVWriter --> CLI: CSV writer ready
deactivate CSVWriter

CLI -> JSONLWriter: JsonlTickWriter(results.jsonl)
activate JSONLWriter
JSONLWriter --> CLI: JSONL writer ready
deactivate JSONLWriter

CLI -> SummaryWriter: SummaryJsonWriter(summary.json)
activate SummaryWriter
SummaryWriter --> CLI: Summary writer ready
deactivate SummaryWriter

== 5. Model & Runner Initialization ==
CLI -> Model: MMG3DOFModel()
activate Model
Model -> Model: Initialize force modules:\nhull, propulsion, rudder, wind, current
Model --> CLI: MMG3DOF model ready
deactivate Model

CLI -> Runner: SimulationRunner()
activate Runner
Runner --> CLI: Runner ready
deactivate Runner

== 6. Main Simulation Loop ==
CLI -> Runner: run(initial_state, config, vessel_params,\nmodel, control_provider, env_provider, writers)
activate Runner

loop Every time step (dt=0.5s) for 60 seconds
    Runner -> Runner: t = current time\nk = step counter\ny = state vector [x,y,ψ,u,v,r]
    
    Runner -> Model: unpack_state_vector(y)
    activate Model
    Model --> Runner: VesselState(x,y,psi,u,v,r)
    deactivate Model
    
    Runner -> ControlProvider: compute(t, state, target)
    activate ControlProvider
    ControlProvider -> Autopilot: Calculate PID outputs
    activate Autopilot
    Autopilot --> ControlProvider: Rate commands (rpm/s, rudder/s)
    deactivate Autopilot
    ControlProvider -> ControlProvider: Integrate rates to absolute:\nrpm_cmd += rate*dt\nrudder_cmd += rate*dt
    ControlProvider --> Runner: ControlInput(rpm=20, rudder=0.1745)
    deactivate ControlProvider
    
    Runner -> EnvProvider: sample(t, state)
    activate EnvProvider
    EnvProvider --> Runner: EnvironmentSample\n(wind_speed=5, wind_dir=π,\ncurrent_speed=0.5, current_dir=0)
    deactivate EnvProvider
    
    Runner -> Model: forces(state, control, env, vessel_params)
    activate Model
    Model -> Forces: Calculate hull forces
    activate Forces
    Forces --> Model: Hull resistance/lift
    deactivate Forces
    
    Model -> Forces: Calculate propulsion forces
    activate Forces
    Forces --> Model: Thrust from 20 RPM
    deactivate Forces
    
    Model -> Forces: Calculate rudder forces
    activate Forces
    Forces --> Model: Steering force from 10° rudder
    deactivate Forces
    
    Model -> Forces: Calculate wind forces
    activate Forces
    Forces --> Model: Wind drag/moment
    deactivate Forces
    
    Model -> Forces: Calculate current forces
    activate Forces
    Forces --> Model: Current drag
    deactivate Forces
    
    Model -> Model: Aggregate all forces:\nX_total, Y_total, N_total
    Model --> Runner: Force dictionary {X, Y, N}
    deactivate Model
    
    Runner -> Model: f(t, state, control, env, vessel_params)
    activate Model
    Model -> Model: Calculate derivatives:\ndx/dt = u*cos(ψ) - v*sin(ψ)\ndy/dt = u*sin(ψ) + v*cos(ψ)\ndψ/dt = r\ndu/dt = (X - m*v*r) / m_eff_x\ndv/dt = (Y + m*u*r) / m_eff_y\ndr/dt = N / Iz_eff
    Model --> Runner: Derivatives [dx,dy,dψ,du,dv,dr]
    deactivate Model
    
    Runner -> SciPy: solve_ivp(fun, [t, t_next], y,\nmethod="RK45", rtol=1e-6)
    activate SciPy
    SciPy -> SciPy: Numerical integration\nRunge-Kutta 4th/5th order
    SciPy --> Runner: Solution with new state y
    deactivate SciPy
    
    alt Every output_decimation steps (decimation=1)
        Runner -> CSVWriter: write_tick(record)
        activate CSVWriter
        CSVWriter -> Files: Append CSV row:\nt,x,y,psi,u,v,r,rpm,rudder_angle,\nwind_speed,wind_dir_from,current_speed,\ncurrent_dir_to,X,Y,N,rpm_cmd,rudder_cmd
        deactivate CSVWriter
        
        Runner -> JSONLWriter: write_tick(record)
        activate JSONLWriter
        JSONLWriter -> Files: Append JSONL line:\n{"t":..., "state":{...}, "control":{...}, "forces":{...}}
        deactivate JSONLWriter
    end
    
    Runner -> Runner: Check termination:\nt >= t_end OR\nout of bounds OR\nsolver failure
    
    Runner -> Runner: t += dt, k += 1
end

Runner -> SummaryWriter: Write final summary
activate SummaryWriter
SummaryWriter -> Files: summary.json:\n{"status": "completed",\n"reason": "time_limit",\n"end_time": 60.0,\n"total_ticks": 120,\n"dt": 0.5}
deactivate SummaryWriter

Runner --> CLI: RunResult(success=True)
deactivate Runner

== 7. Cleanup & Finalization ==
CLI -> CSVWriter: close()
activate CSVWriter
CSVWriter -> Files: Finalize results.csv
deactivate CSVWriter

CLI -> JSONLWriter: close()
activate JSONLWriter
JSONLWriter -> Files: Finalize results.jsonl
deactivate JSONLWriter

== 8. Optional Visualization ==
alt --plot2d flag provided
    CLI -> Plotter: plot_run_csv(results.csv, plot2d.png,\nPlot2DOptions(title="Trajectory"))
    activate Plotter
    Plotter -> Files: Read results.csv
    Files --> Plotter: CSV data with trajectory points
    Plotter -> Plotter: Create matplotlib figure:\n- Plot x,y trajectory\n- Color by speed\n- Add heading arrows\n- Set equal axis, grid
    Plotter -> Files: Save plot2d.png
    Plotter --> CLI: ✓ Plot generated
    deactivate Plotter
end

CLI --> User: ✓ Simulation complete\nOutputs in runs/final_test/

note over Files
**runs/final_test/**
├── results.csv (120 rows × 17 columns)
├── results.jsonl (120 JSON lines)
├── summary.json (run metadata)
└── plot2d.png (trajectory visualization)

**Expected Results:**
- Ship accelerates forward (u increases)
- Turns starboard due to 10° rudder
- Traces circular/spiral path
- Wind pushes from behind
- Current creates sideways drift
- Final position: ~circular arc
end note

deactivate CLI

@enduml